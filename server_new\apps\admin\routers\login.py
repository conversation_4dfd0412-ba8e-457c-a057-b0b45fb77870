import jwt
import uuid
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
from config.enums import BusinessType, RedisInitKeyConfig
from config import settings, AppConfig, JwtConfig
from core.database.manager import get_db
from apps.admin.annotation.log_annotation import Log
from apps.admin.schemas.common import CrudResponseSchema
from apps.admin.schemas.login import UserLoginSchema, UserRegisterSchema, TokenSchema
from apps.admin.schemas.user import CurrentUserSchema, EditUserSchema
from apps.admin.services.login import CustomOAuth2PasswordRequestForm, LoginService, oauth2_scheme
from apps.admin.services.user import UserService
from core.logging.manager import logger
from common.response import ResponseService


router = APIRouter()


@router.post('/login', response_model=TokenSchema)
@Log(title='用户登录', business_type=BusinessType.OTHER, log_type='login')
async def login(
    request: Request, form_data: CustomOAuth2PasswordRequestForm = Depends(), query_db: AsyncSession = Depends(get_db)
):
    captcha_enabled = (
        True
        if await request.app.state.redis.get(f'{RedisInitKeyConfig.SYS_CONFIG.key}:sys.account.captchaEnabled')
        == 'true'
        else False
    )
    user = UserLoginSchema(
        userName=form_data.username,
        password=form_data.password,
        code=form_data.code,
        uuid=form_data.uuid,
        loginInfo=form_data.login_info,
        captchaEnabled=captcha_enabled,
    )
    result = await LoginService.authenticate_user(request, query_db, user)
    access_token_expires = timedelta(minutes=JwtConfig.jwt_expire_minutes)
    session_id = str(uuid.uuid4())
    access_token = await LoginService.create_access_token(
        data={
            'user_id': str(result[0].user_id),
            'user_name': result[0].user_name,
            'dept_name': result[1].dept_name if result[1] else None,
            'session_id': session_id,
            'login_info': user.login_info,
        },
        expires_delta=access_token_expires,
    )
    if settings.app.app_same_time_login:
        await request.app.state.redis.set(
            f'{RedisInitKeyConfig.ACCESS_TOKEN.key}:{session_id}',
            access_token,
            ex=timedelta(minutes=JwtConfig.jwt_redis_expire_minutes),
        )
    else:
        # 此方法可实现同一账号同一时间只能登录一次
        await request.app.state.redis.set(
            f'{RedisInitKeyConfig.ACCESS_TOKEN.key}:{result[0].user_id}',
            access_token,
            ex=timedelta(minutes=JwtConfig.jwt_redis_expire_minutes),
        )
    await UserService.edit_user_services(
        query_db, EditUserSchema(userId=result[0].user_id, loginDate=datetime.now(), type='status')
    )
    logger.info('登录成功')
    # 判断请求是否来自于api文档，如果是返回指定格式的结果，用于修复api文档认证成功后token显示undefined的bug
    request_from_swagger = request.headers.get('referer').endswith('docs') if request.headers.get('referer') else False
    request_from_redoc = request.headers.get('referer').endswith('redoc') if request.headers.get('referer') else False
    if request_from_swagger or request_from_redoc:
        return {'access_token': access_token, 'token_type': 'Bearer'}
    return ResponseService.success(msg='登录成功', dict_content={'token': access_token})


@router.get('/getInfo', response_model=CurrentUserSchema)
async def get_login_user_info(
    request: Request, current_user: CurrentUserSchema = Depends(LoginService.get_current_user)
):
    logger.info('获取成功')

    return ResponseService.success(model_content=current_user)


@router.get('/getRouters')
async def get_login_user_routers(
    request: Request,
    current_user: CurrentUserSchema = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db),
):
    logger.info('获取成功')
    user_routers = await LoginService.get_current_user_routers(current_user.user.user_id, query_db)

    return ResponseService.success(data=user_routers)


@router.post('/register', response_model=CrudResponseSchema)
async def register_user(request: Request, user_register: UserRegisterSchema, query_db: AsyncSession = Depends(get_db)):
    user_register_result = await LoginService.register_user_services(request, query_db, user_register)
    logger.info(user_register_result.message)

    return ResponseService.success(data=user_register_result, msg=user_register_result.message)


# @router.post("/getSmsCode", response_model=SmsCodeSchema)
# async def get_sms_code(request: Request, user: ResetUserSchema, query_db: AsyncSession = Depends(get_db)):
#     try:
#         sms_result = await LoginService.get_sms_code_services(request, query_db, user)
#         if sms_result.is_success:
#             logger.info('获取成功')
#             return ResponseService.success(data=sms_result)
#         else:
#             logger.warning(sms_result.message)
#             return ResponseService.failure(msg=sms_result.message)
#     except Exception as e:
#         logger.exception(e)
#         return ResponseService.error(msg=str(e))
#
#
# @router.post("/forgetPwd", response_model=CrudResponseSchema)
# async def forget_user_pwd(request: Request, forget_user: ResetUserSchema, query_db: AsyncSession = Depends(get_db)):
#     try:
#         forget_user_result = await LoginService.forget_user_services(request, query_db, forget_user)
#         if forget_user_result.is_success:
#             logger.info(forget_user_result.message)
#             return ResponseService.success(data=forget_user_result, msg=forget_user_result.message)
#         else:
#             logger.warning(forget_user_result.message)
#             return ResponseService.failure(msg=forget_user_result.message)
#     except Exception as e:
#         logger.exception(e)
#         return ResponseService.error(msg=str(e))


@router.post('/logout')
async def logout(request: Request, token: Optional[str] = Depends(oauth2_scheme)):
    payload = jwt.decode(
        token, JwtConfig.jwt_secret_key, algorithms=[JwtConfig.jwt_algorithm], options={'verify_exp': False}
    )
    session_id: str = payload.get('session_id')
    await LoginService.logout_services(request, session_id)
    logger.info('退出成功')

    return ResponseService.success(msg='退出成功')
