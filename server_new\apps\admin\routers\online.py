from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from core.database.manager import get_db
from apps.admin.annotation.log_annotation import Log
from apps.admin.aspect.interface_auth import CheckUserInterfaceAuth
from apps.admin.schemas.online import DeleteOnlineSchema, OnlineQuerySchema
from apps.admin.services.login import LoginService
from apps.admin.services.online import OnlineService
from core.logging.manager import logger
from common.page import PageResponseModel
from common.response import ResponseService


router = APIRouter(prefix='/monitor/online', dependencies=[Depends(LoginService.get_current_user)])


@router.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('monitor:online:list'))]
)
async def get_monitor_online_list(
    request: Request, online_page_query: OnlineQuerySchema = Depends(OnlineQuerySchema.as_query)
):
    # 获取全量数据
    online_query_result = await OnlineService.get_online_list_services(request, online_page_query)
    logger.info('获取成功')

    return ResponseService.success(
        model_content=PageResponseModel(rows=online_query_result, total=len(online_query_result))
    )


@router.delete('/{token_ids}', dependencies=[Depends(CheckUserInterfaceAuth('monitor:online:forceLogout'))])
@Log(title='在线用户', business_type=BusinessType.FORCE)
async def delete_monitor_online(request: Request, token_ids: str, query_db: AsyncSession = Depends(get_db)):
    delete_online = DeleteOnlineSchema(tokenIds=token_ids)
    delete_online_result = await OnlineService.delete_online_services(request, delete_online)
    logger.info(delete_online_result.message)

    return ResponseService.success(msg=delete_online_result.message)
