# 生产环境配置文件

# 应用配置
APP_NAME=MXTT-FastAPI
APP_VERSION=1.0.0
APP_ENVIRONMENT=production
APP_HOST=0.0.0.0
APP_PORT=9099
APP_RELOAD=false
APP_ROOT_PATH=/api

# 数据库配置
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_production_password
DB_DATABASE=ruoyi-fastapi
DB_ECHO=false

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_USERNAME=
REDIS_PASSWORD=your_redis_password
REDIS_DATABASE=2

# JWT配置
JWT_SECRET_KEY=your_production_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440
JWT_REDIS_EXPIRE_MINUTES=30

# 上传配置
UPLOAD_PREFIX=/profile
UPLOAD_PATH=storage/uploads
UPLOAD_MACHINE=A
UPLOAD_DOWNLOAD_PATH=storage/downloads

# 代码生成配置
GEN_AUTHOR=mengqb
GEN_PACKAGE_NAME=admin.system
GEN_AUTO_REMOVE_PRE=false
GEN_TABLE_PREFIX=sys_
GEN_ALLOW_OVERWRITE=false
GEN_PATH=storage/generated
