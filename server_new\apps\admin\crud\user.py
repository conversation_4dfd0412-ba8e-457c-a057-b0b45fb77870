from datetime import datetime, time
from sqlalchemy import and_, delete, desc, func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from apps.admin.models.dept import SysDeptModel
from apps.admin.models.menu import SysMenuModel
from apps.admin.models.post import SysPostModel
from apps.admin.models.role import SysRoleModel, SysRoleDeptModel, SysRoleMenuModel  # noqa: F401
from apps.admin.models.user import SysUserModel, SysUserPostModel, SysUserRoleModel
from apps.admin.schemas.user import (
    UserSchema,
    UserPageQuerySchema,
    UserPostSchema,
    UserRoleSchema,
    UserRolePageQuerySchema,
    UserRoleQuerySchema,
)
from common.page import PaginationService


class UserCrud:
    """
    用户管理模块数据库操作层
    """

    @classmethod
    async def get_user_by_name(cls, db: AsyncSession, user_name: str):
        """
        根据用户名获取用户信息

        :param db: orm对象
        :param user_name: 用户名
        :return: 当前用户名的用户信息对象
        """
        query_user_info = (
            (
                await db.execute(
                    select(SysUserModel)
                    .where(SysUserModel.status == '0', SysUserModel.del_flag == '0', SysUserModel.user_name == user_name)
                    .order_by(desc(SysUserModel.create_time))
                    .distinct()
                )
            )
            .scalars()
            .first()
        )

        return query_user_info

    @classmethod
    async def get_user_by_info(cls, db: AsyncSession, user: UserSchema):
        """
        根据用户参数获取用户信息

        :param db: orm对象
        :param user: 用户参数
        :return: 当前用户参数的用户信息对象
        """
        query_user_info = (
            (
                await db.execute(
                    select(SysUserModel)
                    .where(
                        SysUserModel.del_flag == '0',
                        SysUserModel.user_name == user.user_name if user.user_name else True,
                        SysUserModel.phonenumber == user.phonenumber if user.phonenumber else True,
                        SysUserModel.email == user.email if user.email else True,
                    )
                    .order_by(desc(SysUserModel.create_time))
                    .distinct()
                )
            )
            .scalars()
            .first()
        )

        return query_user_info

    @classmethod
    async def get_user_by_id(cls, db: AsyncSession, user_id: int):
        """
        根据user_id获取用户信息

        :param db: orm对象
        :param user_id: 用户id
        :return: 当前user_id的用户信息对象
        """
        query_user_basic_info = (
            (
                await db.execute(
                    select(SysUserModel)
                    .where(SysUserModel.status == '0', SysUserModel.del_flag == '0', SysUserModel.user_id == user_id)
                    .distinct()
                )
            )
            .scalars()
            .first()
        )
        query_user_dept_info = (
            (
                await db.execute(
                    select(SysDeptModel)
                    .select_from(SysUserModel)
                    .where(SysUserModel.status == '0', SysUserModel.del_flag == '0', SysUserModel.user_id == user_id)
                    .join(
                        SysDeptModel,
                        and_(SysUserModel.dept_id == SysDeptModel.dept_id, SysDeptModel.status == '0', SysDeptModel.del_flag == '0'),
                    )
                    .distinct()
                )
            )
            .scalars()
            .first()
        )
        query_user_role_info = (
            (
                await db.execute(
                    select(SysRoleModel)
                    .select_from(SysUserModel)
                    .where(SysUserModel.status == '0', SysUserModel.del_flag == '0', SysUserModel.user_id == user_id)
                    .join(SysUserRoleModel, SysUserModel.user_id == SysUserRoleModel.user_id, isouter=True)
                    .join(
                        SysRoleModel,
                        and_(SysUserRoleModel.role_id == SysRoleModel.role_id, SysRoleModel.status == '0', SysRoleModel.del_flag == '0'),
                    )
                    .distinct()
                )
            )
            .scalars()
            .all()
        )
        query_user_post_info = (
            (
                await db.execute(
                    select(SysPostModel)
                    .select_from(SysUserModel)
                    .where(SysUserModel.status == '0', SysUserModel.del_flag == '0', SysUserModel.user_id == user_id)
                    .join(SysUserPostModel, SysUserModel.user_id == SysUserPostModel.user_id, isouter=True)
                    .join(SysPostModel, and_(SysUserPostModel.post_id == SysPostModel.post_id, SysPostModel.status == '0'))
                    .distinct()
                )
            )
            .scalars()
            .all()
        )
        role_id_list = [item.role_id for item in query_user_role_info]
        if 1 in role_id_list:
            query_user_menu_info = (
                (await db.execute(select(SysMenuModel).where(SysMenuModel.status == '0').distinct())).scalars().all()
            )
        else:
            query_user_menu_info = (
                (
                    await db.execute(
                        select(SysMenuModel)
                        .select_from(SysUserModel)
                        .where(SysUserModel.status == '0', SysUserModel.del_flag == '0', SysUserModel.user_id == user_id)
                        .join(SysUserRoleModel, SysUserModel.user_id == SysUserRoleModel.user_id, isouter=True)
                        .join(
                            SysRoleModel,
                            and_(
                                SysUserRoleModel.role_id == SysRoleModel.role_id, SysRoleModel.status == '0', SysRoleModel.del_flag == '0'
                            ),
                            isouter=True,
                        )
                        .join(SysRoleMenuModel, SysRoleModel.role_id == SysRoleMenuModel.role_id, isouter=True)
                        .join(SysMenuModel, and_(SysRoleMenuModel.menu_id == SysMenuModel.menu_id, SysMenuModel.status == '0'))
                        .order_by(SysMenuModel.order_num)
                        .distinct()
                    )
                )
                .scalars()
                .all()
            )

        results = dict(
            user_basic_info=query_user_basic_info,
            user_dept_info=query_user_dept_info,
            user_role_info=query_user_role_info,
            user_post_info=query_user_post_info,
            user_menu_info=query_user_menu_info,
        )

        return results

    @classmethod
    async def get_user_detail_by_id(cls, db: AsyncSession, user_id: int):
        """
        根据user_id获取用户详细信息

        :param db: orm对象
        :param user_id: 用户id
        :return: 当前user_id的用户信息对象
        """
        query_user_basic_info = (
            (await db.execute(select(SysUserModel).where(SysUserModel.del_flag == '0', SysUserModel.user_id == user_id).distinct()))
            .scalars()
            .first()
        )
        query_user_dept_info = (
            (
                await db.execute(
                    select(SysDeptModel)
                    .select_from(SysUserModel)
                    .where(SysUserModel.del_flag == '0', SysUserModel.user_id == user_id)
                    .join(
                        SysDeptModel,
                        and_(SysUserModel.dept_id == SysDeptModel.dept_id, SysDeptModel.status == '0', SysDeptModel.del_flag == '0'),
                    )
                    .distinct()
                )
            )
            .scalars()
            .first()
        )
        query_user_role_info = (
            (
                await db.execute(
                    select(SysRoleModel)
                    .select_from(SysUserModel)
                    .where(SysUserModel.del_flag == '0', SysUserModel.user_id == user_id)
                    .join(SysUserRoleModel, SysUserModel.user_id == SysUserRoleModel.user_id, isouter=True)
                    .join(
                        SysRoleModel,
                        and_(SysUserRoleModel.role_id == SysRoleModel.role_id, SysRoleModel.status == '0', SysRoleModel.del_flag == '0'),
                    )
                    .distinct()
                )
            )
            .scalars()
            .all()
        )
        query_user_post_info = (
            (
                await db.execute(
                    select(SysPostModel)
                    .select_from(SysUserModel)
                    .where(SysUserModel.del_flag == '0', SysUserModel.user_id == user_id)
                    .join(SysUserPostModel, SysUserModel.user_id == SysUserPostModel.user_id, isouter=True)
                    .join(SysPostModel, and_(SysUserPostModel.post_id == SysPostModel.post_id, SysPostModel.status == '0'))
                    .distinct()
                )
            )
            .scalars()
            .all()
        )
        query_user_menu_info = (
            (
                await db.execute(
                    select(SysMenuModel)
                    .select_from(SysUserModel)
                    .where(SysUserModel.del_flag == '0', SysUserModel.user_id == user_id)
                    .join(SysUserRoleModel, SysUserModel.user_id == SysUserRoleModel.user_id, isouter=True)
                    .join(
                        SysRoleModel,
                        and_(SysUserRoleModel.role_id == SysRoleModel.role_id, SysRoleModel.status == '0', SysRoleModel.del_flag == '0'),
                        isouter=True,
                    )
                    .join(SysRoleMenuModel, SysRoleModel.role_id == SysRoleMenuModel.role_id, isouter=True)
                    .join(SysMenuModel, and_(SysRoleMenuModel.menu_id == SysMenuModel.menu_id, SysMenuModel.status == '0'))
                    .distinct()
                )
            )
            .scalars()
            .all()
        )
        results = dict(
            user_basic_info=query_user_basic_info,
            user_dept_info=query_user_dept_info,
            user_role_info=query_user_role_info,
            user_post_info=query_user_post_info,
            user_menu_info=query_user_menu_info,
        )

        return results

    @classmethod
    async def get_user_list(
        cls, db: AsyncSession, query_object: UserPageQuerySchema, data_scope_sql: str, is_page: bool = False
    ):
        """
        根据查询参数获取用户列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param data_scope_sql: 数据权限对应的查询sql语句
        :param is_page: 是否开启分页
        :return: 用户列表信息对象
        """
        query = (
            select(SysUserModel, SysDeptModel)
            .where(
                SysUserModel.del_flag == '0',
                or_(
                    SysUserModel.dept_id == query_object.dept_id,
                    SysUserModel.dept_id.in_(
                        select(SysDeptModel.dept_id).where(func.find_in_set(query_object.dept_id, SysDeptModel.ancestors))
                    ),
                )
                if query_object.dept_id
                else True,
                SysUserModel.user_id == query_object.user_id if query_object.user_id is not None else True,
                SysUserModel.user_name.like(f'%{query_object.user_name}%') if query_object.user_name else True,
                SysUserModel.nick_name.like(f'%{query_object.nick_name}%') if query_object.nick_name else True,
                SysUserModel.email.like(f'%{query_object.email}%') if query_object.email else True,
                SysUserModel.phonenumber.like(f'%{query_object.phonenumber}%') if query_object.phonenumber else True,
                SysUserModel.status == query_object.status if query_object.status else True,
                SysUserModel.sex == query_object.sex if query_object.sex else True,
                SysUserModel.create_time.between(
                    datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(00, 00, 00)),
                    datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59)),
                )
                if query_object.begin_time and query_object.end_time
                else True,
                eval(data_scope_sql),
            )
            .join(
                SysDeptModel,
                and_(SysUserModel.dept_id == SysDeptModel.dept_id, SysDeptModel.status == '0', SysDeptModel.del_flag == '0'),
                isouter=True,
            )
            .order_by(SysUserModel.user_id)
            .distinct()
        )
        user_list = await PaginationService.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return user_list

    @classmethod
    async def add_user_dao(cls, db: AsyncSession, user: UserSchema):
        """
        新增用户数据库操作

        :param db: orm对象
        :param user: 用户对象
        :return: 新增校验结果
        """
        db_user = SysUserModel(**user.model_dump(exclude={'admin'}))
        db.add(db_user)
        await db.flush()

        return db_user

    @classmethod
    async def edit_user_dao(cls, db: AsyncSession, user: dict):
        """
        编辑用户数据库操作

        :param db: orm对象
        :param user: 需要更新的用户字典
        :return: 编辑校验结果
        """
        await db.execute(update(SysUserModel), [user])

    @classmethod
    async def delete_user_dao(cls, db: AsyncSession, user: UserSchema):
        """
        删除用户数据库操作

        :param db: orm对象
        :param user: 用户对象
        :return:
        """
        await db.execute(
            update(SysUserModel)
            .where(SysUserModel.user_id == user.user_id)
            .values(del_flag='2', update_by=user.update_by, update_time=user.update_time)
        )

    @classmethod
    async def get_user_role_allocated_list_by_user_id(cls, db: AsyncSession, query_object: UserRoleQuerySchema):
        """
        根据用户id获取用户已分配的角色列表信息数据库操作

        :param db: orm对象
        :param query_object: 用户角色查询对象
        :return: 用户已分配的角色列表信息
        """
        allocated_role_list = (
            (
                await db.execute(
                    select(SysRoleModel)
                    .where(
                        SysRoleModel.del_flag == '0',
                        SysRoleModel.role_id != 1,
                        SysRoleModel.role_name == query_object.role_name if query_object.role_name else True,
                        SysRoleModel.role_key == query_object.role_key if query_object.role_key else True,
                        SysRoleModel.role_id.in_(
                            select(SysUserRoleModel.role_id).where(SysUserRoleModel.user_id == query_object.user_id)
                        ),
                    )
                    .distinct()
                )
            )
            .scalars()
            .all()
        )

        return allocated_role_list

    @classmethod
    async def get_user_role_allocated_list_by_role_id(
        cls, db: AsyncSession, query_object: UserRolePageQuerySchema, data_scope_sql: str, is_page: bool = False
    ):
        """
        根据角色id获取已分配的用户列表信息

        :param db: orm对象
        :param query_object: 用户角色查询对象
        :param data_scope_sql: 数据权限对应的查询sql语句
        :param is_page: 是否开启分页
        :return: 角色已分配的用户列表信息
        """
        query = (
            select(SysUserModel)
            .join(SysDeptModel, SysDeptModel.dept_id == SysUserModel.dept_id, isouter=True)
            .join(SysUserRoleModel, SysUserRoleModel.user_id == SysUserModel.user_id, isouter=True)
            .join(SysRoleModel, SysRoleModel.role_id == SysUserRoleModel.role_id, isouter=True)
            .where(
                SysUserModel.del_flag == '0',
                SysUserModel.user_name == query_object.user_name if query_object.user_name else True,
                SysUserModel.phonenumber == query_object.phonenumber if query_object.phonenumber else True,
                SysRoleModel.role_id == query_object.role_id,
                eval(data_scope_sql),
            )
            .distinct()
        )
        allocated_user_list = await PaginationService.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return allocated_user_list

    @classmethod
    async def get_user_role_unallocated_list_by_role_id(
        cls, db: AsyncSession, query_object: UserRolePageQuerySchema, data_scope_sql: str, is_page: bool = False
    ):
        """
        根据角色id获取未分配的用户列表信息

        :param db: orm对象
        :param query_object: 用户角色查询对象
        :param data_scope_sql: 数据权限对应的查询sql语句
        :param is_page: 是否开启分页
        :return: 角色未分配的用户列表信息
        """
        query = (
            select(SysUserModel)
            .join(SysDeptModel, SysDeptModel.dept_id == SysUserModel.dept_id, isouter=True)
            .join(SysUserRoleModel, SysUserRoleModel.user_id == SysUserModel.user_id, isouter=True)
            .join(SysRoleModel, SysRoleModel.role_id == SysUserRoleModel.role_id, isouter=True)
            .where(
                SysUserModel.del_flag == '0',
                SysUserModel.user_name == query_object.user_name if query_object.user_name else True,
                SysUserModel.phonenumber == query_object.phonenumber if query_object.phonenumber else True,
                or_(SysRoleModel.role_id != query_object.role_id, SysRoleModel.role_id.is_(None)),
                ~SysUserModel.user_id.in_(
                    select(SysUserModel.user_id)
                    .select_from(SysUserModel)
                    .join(
                        SysUserRoleModel,
                        and_(SysUserRoleModel.user_id == SysUserModel.user_id, SysUserRoleModel.role_id == query_object.role_id),
                    )
                ),
                eval(data_scope_sql),
            )
            .distinct()
        )
        unallocated_user_list = await PaginationService.paginate(
            db, query, query_object.page_num, query_object.page_size, is_page
        )

        return unallocated_user_list

    @classmethod
    async def add_user_role_dao(cls, db: AsyncSession, user_role: UserRoleSchema):
        """
        新增用户角色关联信息数据库操作

        :param db: orm对象
        :param user_role: 用户角色关联对象
        :return:
        """
        db_user_role = SysUserRoleModel(**user_role.model_dump())
        db.add(db_user_role)

    @classmethod
    async def delete_user_role_dao(cls, db: AsyncSession, user_role: UserRoleSchema):
        """
        删除用户角色关联信息数据库操作

        :param db: orm对象
        :param user_role: 用户角色关联对象
        :return:
        """
        await db.execute(delete(SysUserRoleModel).where(SysUserRoleModel.user_id.in_([user_role.user_id])))

    @classmethod
    async def delete_user_role_by_user_and_role_dao(cls, db: AsyncSession, user_role: UserRoleSchema):
        """
        根据用户id及角色id删除用户角色关联信息数据库操作

        :param db: orm对象
        :param user_role: 用户角色关联对象
        :return:
        """
        await db.execute(
            delete(SysUserRoleModel).where(
                SysUserRoleModel.user_id == user_role.user_id if user_role.user_id else True,
                SysUserRoleModel.role_id == user_role.role_id if user_role.role_id else True,
            )
        )

    @classmethod
    async def get_user_role_detail(cls, db: AsyncSession, user_role: UserRoleSchema):
        """
        根据用户角色关联获取用户角色关联详细信息

        :param db: orm对象
        :param user_role: 用户角色关联对象
        :return: 用户角色关联信息
        """
        user_role_info = (
            (
                await db.execute(
                    select(SysUserRoleModel)
                    .where(SysUserRoleModel.user_id == user_role.user_id, SysUserRoleModel.role_id == user_role.role_id)
                    .distinct()
                )
            )
            .scalars()
            .first()
        )

        return user_role_info

    @classmethod
    async def add_user_post_dao(cls, db: AsyncSession, user_post: UserPostSchema):
        """
        新增用户岗位关联信息数据库操作

        :param db: orm对象
        :param user_post: 用户岗位关联对象
        :return:
        """
        db_user_post = SysUserPostModel(**user_post.model_dump())
        db.add(db_user_post)

    @classmethod
    async def delete_user_post_dao(cls, db: AsyncSession, user_post: UserPostSchema):
        """
        删除用户岗位关联信息数据库操作

        :param db: orm对象
        :param user_post: 用户岗位关联对象
        :return:
        """
        await db.execute(delete(SysUserPostModel).where(SysUserPostModel.user_id.in_([user_post.user_id])))

    @classmethod
    async def get_user_dept_info(cls, db: AsyncSession, dept_id: int):
        dept_basic_info = (
            (
                await db.execute(
                    select(SysDeptModel).where(SysDeptModel.dept_id == dept_id, SysDeptModel.status == '0', SysDeptModel.del_flag == '0')
                )
            )
            .scalars()
            .first()
        )
        return dept_basic_info
